stages:
  - test
  - build
  - deploy

variables:
  SSH_PRIVATE_KEY: $SSH_PRIVATE_KEY
  VPS_USER: gitlab
  VPS_HOST: ***************
  VPS_PATH: /home/<USER>/yotracker

test:
    stage: test
    image: golang:1.22
    services:
      - mysql:8.0
    variables:
      MYSQL_DATABASE: yotracker
      MYSQL_USER: admin
      MYSQL_PASSWORD: password
      MYSQL_ROOT_PASSWORD: password
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
      DB_HOST: mysql
      DB_PORT: "3306"
      DB_USERNAME: admin
      DB_PASSWORD: password
      DB_NAME: yotracker
      TESTING_DB_NAME: testing
    before_script:
      - apt-get update -qq && apt-get install -y -qq git default-mysql-client
      - mysql --host=mysql --user=root --password=password -e "CREATE DATABASE IF NOT EXISTS testing;"
      - mysql --host=mysql --user=root --password=password -e "GRANT ALL PRIVILEGES ON testing.* TO 'admin'@'%';"
      - export DB_HOST=mysql
      - export DB_PORT=3306
      - export DB_USERNAME=admin
      - export DB_PASSWORD=password
      - export DB_NAME=yotracker
      - export TESTING_DB_NAME=testing
      - export GT06_PORT=5022
      - export H02_PORT=5010
      - export BACKEND_HOST=0.0.0.0
      - export BACKEND_PORT=9000
      - export APP_KEY=secret
    script:
      - echo "Running tests..."
      - go test ./...

build:

  stage: build
  image: golang:1.22
  script:
    - echo "Building Go applications..."
    - cd cmd/server && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o tcp_server && cd ../..
    - cd cmd/web/backend &&CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o backend && cd ../../..
    #- cd  cmd/web/frontend && go build -o frontend && cd ../../..
  artifacts:
    paths:
      - cmd/server/tcp_server
      - cmd/web/backend/backend
deploy:
  stage: deploy
  image: ubuntu:20.04
  before_script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null # Adding private key for SSH access
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
  script:
    - echo "Deploying application..."
    # Copy built binaries to the VPS
    - echo "Copying binaries to server..."
    - ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST 'sudo supervisorctl stop yotracker-tcp_server && sudo supervisorctl stop yotracker-backend'
    - scp -o StrictHostKeyChecking=no cmd/server/tcp_server $VPS_USER@$VPS_HOST:$VPS_PATH/tcp_server
    - scp -o StrictHostKeyChecking=no cmd/web/backend/backend $VPS_USER@$VPS_HOST:$VPS_PATH/backend
    # Restart the applications using supervisor or systemd
    - echo "Restarting the applications..."
    #- ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST 'sudo systemctl restart supervisor'
    - ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST 'sudo supervisorctl start yotracker-tcp_server && sudo supervisorctl start yotracker-backend'

    - echo "Deployment completed successfully!"
  only:
    - main # Run this job only on the main branch