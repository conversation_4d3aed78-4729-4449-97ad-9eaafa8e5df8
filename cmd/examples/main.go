package main

import (
	"fmt"
	"yotracker/examples"
)

func main() {
	fmt.Println("=== YoTracker Mail Service Examples ===")
	fmt.Println()

	// Run basic mail usage examples
	fmt.Println("1. Basic Mail Usage Examples:")
	fmt.Println("-----------------------------")
	examples.ExampleMailUsage()
	fmt.Println()

	// Run template usage examples
	fmt.Println("2. Template Usage Examples:")
	fmt.Println("---------------------------")
	examples.ExampleTemplateUsage()
	fmt.Println()

	// Run new account email examples
	fmt.Println("3. New Account Email Examples:")
	fmt.Println("------------------------------")
	examples.RunNewAccountEmailExamples()
	fmt.Println()

	// Run client welcome email examples
	fmt.Println("4. Client Welcome Email Examples:")
	fmt.Println("----------------------------------")
	examples.RunClientWelcomeEmailExamples()
	fmt.Println()

	// Show controller integration
	fmt.Println("5. Controller Integration Example:")
	fmt.Println("----------------------------------")
	examples.ExampleSetupMailRoutes()
	fmt.Println("Controller integration examples are available in examples/controller_integration.go")
	fmt.Println()

	fmt.Println("=== All Examples Completed ===")
	fmt.Println()
	fmt.Println("To run individual examples:")
	fmt.Println("• Basic mail usage: examples.ExampleMailUsage()")
	fmt.Println("• Template usage: examples.ExampleTemplateUsage()")
	fmt.Println("• New account emails: examples.ExampleNewAccountEmail()")
	fmt.Println("• Client welcome emails: examples.ExampleClientWelcomeEmail()")
	fmt.Println("• Controller setup: examples.ExampleSetupMailRoutes()")
	fmt.Println()
	fmt.Println("Note: These examples use fake API keys and won't actually send emails.")
	fmt.Println("Set up your SendGrid API key in .env to send real emails.")
}
