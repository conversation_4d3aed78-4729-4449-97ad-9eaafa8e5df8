package main

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"os"
	"yotracker/cmd/web/frontend/controllers"
	"yotracker/cmd/web/frontend/routes"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
)

func main() {
	//connect to db
	config.InitDB()
	hub := controllers.NewHub()
	go hub.Run()
	r := SetupRouter(hub)
	err := r.Run(os.Getenv("BACKEND_HOST") + ":" + os.Getenv("FRONTEND_PORT"))
	if err != nil {
		fmt.Println("Failed to start frontend server")
	}
}
func SetupRouter(hub *controllers.Hub) *gin.Engine {
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	routes.FrontendRoutes(hub, r)
	return r

}
