package controllers

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

type LoginInput struct {
	Email    string `form:"email" json:"email" binding:"required"`
	Password string `form:"password" json:"password" binding:"required"`
}

func Home(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "You have successfully connected",
	})
}
func Login(c *gin.Context) {
	var login LoginInput
	err := c.ShouldBindJSON(&login)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}
	var result models.User
	userType := "client"
	err = config.DB.Preload("Client").Where("sms", 1).Model(models.User{Email: login.Email, UserType: userType}).First(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{
				"message": "User not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": err.Error(),
			})
		}
		return
	}
	if !service.CheckPassword(result.Password, login.Password) {
		c.JSON(422, gin.H{
			"message": "Invalid login details",
		})
		return
	}
	fmt.Println(login, result, result.Client)
	if result.Client.Status != "active" {
		c.JSON(422, gin.H{
			"message": "Client is not active",
		})
		return
	}
	// SELECT * FROM users WHERE id = 10;
	token, er := service.GenerateToken(&result, "access")
	if er != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": er.Error(),
		})
		return
	}
	//set last login date
	config.DB.Model(&result).Update("last_login_date", time.Now())
	c.JSON(200, gin.H{
		"token": token,
	})
}
func Register(c *gin.Context) {

}
