package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllAlerts(c *gin.Context) {
	var alerts []models.Alert
	var total int64
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}

	config.DB.Scopes(utils.Paginate(c)).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Preload("ClientDevice").Preload("ClientDevice.Client").Where(filter).Order("id desc").Find(&alerts)
	config.DB.Model(&models.Alert{}).Where(filter).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Count(&total)
	c.JSON(http.StatusOK, gin.H{
		"data":         alerts,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}

func GetAlertById(c *gin.Context) {
	var alert models.Alert
	clientId, _ := c.Get("client_id")
	if err := config.DB.Preload("ClientDevice").Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Preload("ClientDevice.Client").First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": alert,
	})
}

func SearchAlerts(c *gin.Context) {
	var alerts []models.Alert
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}

	config.DB.Where(filter).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Preload("ClientDevice").Preload("ClientDevice.Client").Order("id desc").Find(&alerts)
	c.JSON(http.StatusOK, gin.H{
		"data": alerts,
	})
}
