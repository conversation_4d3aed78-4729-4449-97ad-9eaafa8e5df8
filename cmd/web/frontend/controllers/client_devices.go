package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllClientDevices(c *gin.Context) {
	var clientDevices []models.ClientDevice
	var total int64
	query := config.DB.Preload("DeviceType").Preload("Fleet")
	filter := map[string]interface{}{}
	clientId, _ := c.Get("client_id")
	filter["client_id"] = clientId
	if fleetId := c.Query("fleet_id"); fleetId != "" {
		filter["fleet_id"] = fleetId
	}
	if deviceTypeId := c.Query("device_type_id"); deviceTypeId != "" {
		filter["device_type_id"] = deviceTypeId
	}
	if noPagination := c.Query("no_pagination"); noPagination == "" {
		query = query.Scopes(utils.Paginate(c))
	}
	query.Where(filter).Find(&clientDevices)
	config.DB.Model(&models.ClientDevice{}).Where(filter).Count(&total)
	c.<PERSON>(http.StatusOK, gin.H{
		"data":         clientDevices,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}
func GetClientDeviceById(c *gin.Context) {
	var clientDevice models.ClientDevice
	if err := config.DB.Preload("DeviceType").Preload("DeviceType.Protocol").Preload("Client").Preload("Fleet").First(&clientDevice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client device not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": clientDevice,
	})
}
func UpdateClientDevice(c *gin.Context) {
	var req models.ClientDeviceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var clientDevice models.ClientDevice
	if err := config.DB.First(&clientDevice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client device not found",
		})
		return
	}
	clientDevice.Name = req.Name
	clientDevice.ClientId = req.ClientId
	clientDevice.DeviceTypeId = req.DeviceTypeId
	clientDevice.AssetType = req.AssetType
	clientDevice.DriverId = req.DriverId
	clientDevice.FleetId = req.FleetId
	clientDevice.PhoneNumber = req.PhoneNumber
	clientDevice.DeviceId = req.DeviceId
	clientDevice.Password = req.Password
	clientDevice.AlertPhoneNumber = req.AlertPhoneNumber
	clientDevice.PlateNumber = req.PlateNumber
	clientDevice.MaxSpeed = req.MaxSpeed
	clientDevice.SubscriptionStartDate = req.SubscriptionStartDate
	clientDevice.SubscriptionEndDate = req.SubscriptionEndDate
	clientDevice.Icon = req.Icon
	clientDevice.Model = req.Model
	clientDevice.Make = req.Make
	clientDevice.ManufactureYear = req.ManufactureYear
	clientDevice.Color = req.Color
	clientDevice.EngineNumber = req.EngineNumber
	clientDevice.ChassisNumber = req.ChassisNumber
	clientDevice.Vin = req.Vin
	clientDevice.ObdProtocol = req.ObdProtocol
	clientDevice.FuelType = req.FuelType
	clientDevice.FuelRate = req.FuelRate
	clientDevice.FuelRateUnit = req.FuelRateUnit
	clientDevice.Manufacturer = req.Manufacturer
	clientDevice.Status = req.Status
	clientDevice.InsuranceExpiryDate = req.InsuranceExpiryDate
	clientDevice.InitialMileage = req.InitialMileage
	clientDevice.Mileage = req.Mileage
	clientDevice.CurrentMileage = req.CurrentMileage
	clientDevice.ServiceMileage = req.ServiceMileage
	clientDevice.ServiceDays = req.ServiceDays
	clientDevice.LastServiceDate = req.LastServiceDate
	clientDevice.NextServiceDate = req.NextServiceDate
	clientDevice.LastServiceMileage = req.LastServiceMileage
	clientDevice.NextServiceMileage = req.NextServiceMileage
	clientDevice.Emails = req.Emails
	clientDevice.PhoneNumbers = req.PhoneNumbers
	clientDevice.Description = req.Description
	clientDevice.Amount = req.Amount
	clientDevice.BillingCycle = req.BillingCycle
	clientDevice.NextBillingDate = req.NextBillingDate
	clientDevice.IsLifetime = req.IsLifetime

	result := config.DB.Save(&clientDevice)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Client device updated successfully"})
}
func SearchClientDevices(c *gin.Context) {
	var clientDevices []models.ClientDevice
	filter := map[string]interface{}{}
	if fleetId := c.Query("fleet_id"); fleetId != "" {
		filter["fleet_id"] = fleetId
	}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if deviceTypeId := c.Query("device_type_id"); deviceTypeId != "" {
		filter["device_type_id"] = deviceTypeId
	}
	query := config.DB.Preload("DeviceType").Preload("Client").Preload("Fleet")
	if search := c.Query("s"); search != "" {
		query.Where("name like ? or id like ? or device_id like ? or phone_number like ? or engine_number like ? or chassis_number like ? or vin like ? or plate_number like ?", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}
	query.Where(filter).Find(&clientDevices)
	c.JSON(http.StatusOK, gin.H{
		"data": clientDevices,
	})
}
