package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
)

func SearchGpsData(c *gin.Context) {
	var gpsData []models.GPSData
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}

	config.DB.Where(filter).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = gps_data.client_device_id").Where("gps_timestamp BETWEEN ? AND ?", c.Query("start_date"), c.Query("end_date")).Order("id").Find(&gpsData)
	c.<PERSON>(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
func GetLastLocation(c *gin.Context) {
	var gpsData models.GPSData
	clientId, _ := c.Get("client_id")
	config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = gps_data.client_device_id").Where("client_device_id = ?", c.Query("client_device_id")).Last(&gpsData)
	c.JSON(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
func GetGpsDataById(c *gin.Context) {
	var gpsData models.GPSData
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = gps_data.client_device_id").First(&gpsData, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Gps data not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
