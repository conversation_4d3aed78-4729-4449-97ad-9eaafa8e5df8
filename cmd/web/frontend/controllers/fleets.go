package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
)

func GetAllFleets(c *gin.Context) {
	var fleets []models.Fleet
	clientId, _ := c.Get("client_id")
	config.DB.Find(&fleets, "client_id=?", clientId)
	c.<PERSON>(http.StatusOK, gin.H{
		"data": fleets,
	})
}
func GetFleetById(c *gin.Context) {
	var fleet models.Fleet
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).First(&fleet, c.Param("id")).Error; err != nil {
		c.J<PERSON>(http.StatusNotFound, gin.H{
			"message": "Fleet not found",
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"data": fleet,
	})
}
func CreateFleet(c *gin.Context) {
	var req models.FleetRequest
	if err := c.<PERSON>d<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"message": err.<PERSON><PERSON>r(),
		})
		return
	}
	clientId, _ := c.Get("client_id")
	fleet := models.Fleet{
		Name:         req.Name,
		ParentId:     req.ParentId,
		ClientId:     clientId.(uint),
		Description:  req.Description,
		Emails:       req.Emails,
		PhoneNumbers: req.PhoneNumbers,
	}
	result := config.DB.Create(&fleet)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Fleet created successfully",
	})
}
func UpdateFleet(c *gin.Context) {
	var req models.FleetRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var fleet models.Fleet
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).First(&fleet, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Fleet not found",
		})
		return
	}
	fleet.Name = req.Name
	fleet.Description = req.Description
	fleet.Emails = req.Emails
	fleet.PhoneNumbers = req.PhoneNumbers
	result := config.DB.Save(&fleet)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Fleet updated successfully"})
}
func DeleteFleet(c *gin.Context) {
	var fleet models.Fleet
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).First(&fleet, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Fleet not found",
		})
		return
	}
	result := config.DB.Delete(&fleet)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Fleet deleted successfully"})
}
func SearchFleets(c *gin.Context) {
	var fleets []models.Fleet
	clientId, _ := c.Get("client_id")
	config.DB.Where("client_id = ?", clientId).Find(&fleets, "name like ?", "%"+c.Query("s")+"%")
	c.JSON(http.StatusOK, gin.H{
		"data": fleets,
	})
}
