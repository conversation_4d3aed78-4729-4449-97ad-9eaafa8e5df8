package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllPayments(c *gin.Context) {
	var payments []models.InvoicePayment
	var total int64
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if invoiceId := c.Query("invoice_id"); invoiceId != "" {
		filter["invoice_id"] = invoiceId
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}

	config.DB.Scopes(utils.Paginate(c)).Where("client_id = ?", clientId).Joins("left join invoices on invoices.id = invoice_payments.invoice_id").Preload("PaymentType").Preload("Currency").Preload("Invoice").Preload("Invoice.Client").Where(filter).Order("id desc").Find(&payments)
	config.DB.Where("client_id = ?", clientId).Joins("left join invoices on invoices.id = invoice_payments.invoice_id").Model(&models.InvoicePayment{}).Where(filter).Count(&total)
	c.JSON(http.StatusOK, gin.H{
		"data":         payments,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}

func GetPaymentById(c *gin.Context) {
	var payment models.InvoicePayment
	if err := config.DB.Preload("PaymentType").Preload("Currency").Preload("Invoice").Preload("Invoice.Client").Preload("PaymentType").First(&payment, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Payment not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": payment,
	})
}

func CreatePayment(c *gin.Context) {
	var req models.InvoicePaymentRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)

	payment := models.InvoicePayment{
		InvoiceId:     req.InvoiceId,
		CreatedById:   &userID,
		PaymentTypeId: req.PaymentTypeId,
		CurrencyId:    req.CurrencyId,
		Date:          req.Date,
		TransId:       req.TransId,
		Amount:        req.Amount,
		Xrate:         req.Xrate,
	}
	result := config.DB.Create(&payment)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	UpdateInvoiceStatus(payment.InvoiceId)
	c.JSON(http.StatusOK, gin.H{
		"message": "Payment created successfully",
	})
}

func SearchPayments(c *gin.Context) {
	var payments []models.InvoicePayment
	filter := map[string]interface{}{}
	clientId, _ := c.Get("client_id")
	filter["client_id"] = clientId
	if invoiceId := c.Query("invoice_id"); invoiceId != "" {
		filter["invoice_id"] = invoiceId
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}
	query := config.DB.Joins("left join invoices on invoices.id = invoice_payments.invoice_id").Where(filter).Preload("PaymentType").Preload("Currency").Preload("Invoice")
	if search := c.Query("s"); search != "" {
		query.Where("trans_id LIKE ? or id LIKE ?", "%"+search+"%", "%"+search+"%")
	}
	query.Order("id desc").Find(&payments)
	query.Find(&payments)
	c.JSON(http.StatusOK, gin.H{
		"data": payments,
	})
}
