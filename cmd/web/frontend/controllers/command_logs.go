package controllers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

func GetAllCommandLogs(c *gin.Context) {
	var commandLogs []models.CommandLog
	var total int64
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if commandType := c.Query("command_type"); commandType != "" {
		filter["command_type"] = commandType
	}

	config.DB.Scopes(utils.Paginate(c)).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = command_logs.client_device_id").Preload("ClientDevice").Preload("ClientDevice.Client").Where(filter).Order("id desc").Find(&commandLogs)
	config.DB.Model(&models.CommandLog{}).Where(filter).Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = command_logs.client_device_id").Count(&total)
	c.JSON(http.StatusOK, gin.H{
		"data":         commandLogs,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}

func GetCommandLogById(c *gin.Context) {
	var commandLog models.CommandLog
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = command_logs.client_device_id").Preload("ClientDevice").Preload("ClientDevice.Client").First(&commandLog, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Command log not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": commandLog,
	})
}

func CreateCommandLog(c *gin.Context) {
	var req models.CreateCommandLogRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	selectType := req.SelectType
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)

	var clientDevices []models.ClientDevice
	var clientDeviceIds []uint
	if req.ClientDeviceId != nil {
		clientDeviceIds = append(clientDeviceIds, *req.ClientDeviceId)
	}
	if selectType == "fleet" {
		if req.FleetId == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Fleet id is required",
			})
			return
		}
		var fleet models.Fleet
		results := config.DB.Where("fleet_id", req.FleetId).Find(&clientDevices)
		if results.Error != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": results.Error.Error(),
			})
			return
		}
		for _, clientDevice := range clientDevices {
			clientDeviceIds = append(clientDeviceIds, clientDevice.Id)
		}
		clientDeviceIds = append(clientDeviceIds, fleet.ClientId)
	}
	if selectType == "client" {
		if req.ClientId == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Client id is required",
			})
			return
		}
		clientDevices = []models.ClientDevice{}
		results := config.DB.Where("client_id", req.ClientId).Find(&clientDevices)
		if results.Error != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": results.Error.Error(),
			})
			return
		}
		for _, clientDevice := range clientDevices {
			clientDeviceIds = append(clientDeviceIds, clientDevice.Id)
		}
	}
	if selectType == "device_type" {
		if req.DeviceTypeId == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Device type id is required",
			})
			return
		}
		clientDevices = []models.ClientDevice{}
		results := config.DB.Where("device_type_id", req.DeviceTypeId).Find(&clientDevices)
		if results.Error != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": results.Error.Error(),
			})
			return
		}
		for _, clientDevice := range clientDevices {
			clientDeviceIds = append(clientDeviceIds, clientDevice.Id)
		}
	}
	if selectType == "driver" {
		clientDevices = []models.ClientDevice{}
		results := config.DB.Where("driver_id", req.DriverId).Find(&clientDevices)
		if results.Error != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": results.Error.Error(),
			})
			return
		}
		for _, clientDevice := range clientDevices {
			clientDeviceIds = append(clientDeviceIds, clientDevice.Id)
		}
	}
	//check if we have fleet_id
	if req.FleetId != nil {
		var fleet models.Fleet
		results := config.DB.Where("fleet_id", req.FleetId).Find(&clientDevices)
		if results.Error != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": results.Error.Error(),
			})
			return
		}
		for _, clientDevice := range clientDevices {
			clientDeviceIds = append(clientDeviceIds, clientDevice.Id)
		}
		clientDeviceIds = append(clientDeviceIds, fleet.ClientId)
	}

	for _, clientDeviceId := range clientDeviceIds {
		commandLog := models.CommandLog{
			CreatedById:    &userID,
			ClientDeviceId: clientDeviceId,
			Name:           req.Name,
			CommandType:    req.CommandType,
			CommandContent: req.CommandContent,
			Parameters:     req.Parameters,
			Recurring:      req.Recurring,
			RecurType:      req.RecurType,
			RecurFrequency: req.RecurFrequency,
			RecurLastDate:  req.RecurLastDate,
			RecurNextDate:  req.RecurNextDate,
			ScheduledAt:    req.ScheduledAt,
		}
		result := config.DB.Create(&commandLog)
		if result.Error != nil {
			fmt.Println(result.Error)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Command created successfully",
	})
}

func UpdateCommandLog(c *gin.Context) {
	var req models.UpdateCommandLogRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var commandLog models.CommandLog
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = command_logs.client_device_id").First(&commandLog, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Command log not found",
		})
		return
	}
	commandLog.Status = *req.Status
	commandLog.Parameters = req.Parameters
	commandLog.Recurring = req.Recurring
	commandLog.RecurType = req.RecurType
	commandLog.RecurFrequency = req.RecurFrequency
	commandLog.RecurLastDate = req.RecurLastDate
	commandLog.RecurNextDate = req.RecurNextDate
	commandLog.ScheduledAt = req.ScheduledAt
	result := config.DB.Save(&commandLog)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Command log updated successfully",
	})
}

func DeleteCommandLog(c *gin.Context) {
	var commandLog models.CommandLog
	clientId, _ := c.Get("client_id")
	if err := config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = command_logs.client_device_id").First(&commandLog, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Command log not found",
		})
		return
	}

	result := config.DB.Delete(&commandLog)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Command log deleted successfully",
	})
}

func SearchCommandLogs(c *gin.Context) {
	var commandLogs []models.CommandLog
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if name := c.Query("name"); name != "" {
		config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = command_logs.client_device_id").Preload("ClientDevice").Preload("ClientDevice.Client").Where(filter).Where("name LIKE ?", "%"+name+"%").Order("id desc").Find(&commandLogs)
	} else {
		config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = command_logs.client_device_id").Preload("ClientDevice").Preload("ClientDevice.Client").Where(filter).Order("id desc").Find(&commandLogs)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": commandLogs,
	})
}
