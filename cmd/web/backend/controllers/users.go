package controllers

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"strconv"
	"strings"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/response"
	"yotracker/internal/service"
	"yotracker/internal/utils"
)

func GetAllUsers(c *gin.Context) {
	var users []models.User
	var total int64
	filter := map[string]interface{}{}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}

	config.DB.Scopes(utils.Paginate(c)).Where(filter).Preload("Client").Order("id desc").Find(&users)
	totalResult := config.DB.Model(&models.User{}).Where(filter).Count(&total)
	if totalResult.Error != nil {
		fmt.Println("Failed to get count")
	}
	// Print all users
	var userResponses []response.UserResponse
	for _, user := range users {
		userResponse := response.UserResponse{
			Id:            user.Id,
			Name:          user.Name,
			Email:         user.Email,
			Gender:        user.Gender,
			Description:   user.Description,
			CreatedAt:     utils.FormatTimeOrEmptyString(&user.CreatedAt),
			LastLoginDate: utils.FormatTimeOrEmptyString(user.LastLoginDate),
			Status:        user.Status,
			UserType:      user.UserType,
			Client:        user.Client,
		}
		userResponses = append(userResponses, userResponse)
	}
	c.JSON(200, gin.H{
		"data":         userResponses,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}
func GetUserById(c *gin.Context) {
	userId, _ := strconv.Atoi(c.Param("id"))
	user := models.User{
		Id: uint(userId),
	}
	result := config.DB.Preload("Client").First(&user)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		c.JSON(400, gin.H{
			"message": "user not found",
		})
		return
	} else if result.Error != nil {
		c.JSON(400, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	userResponse := response.UserResponse{
		Id:              user.Id,
		ClientId:        user.ClientId,
		Name:            user.Name,
		Email:           user.Email,
		Gender:          user.Gender,
		Description:     user.Description,
		CreatedAt:       utils.FormatTimeOrEmptyString(&user.CreatedAt),
		LastLoginDate:   utils.FormatTimeOrEmptyString(user.LastLoginDate),
		Status:          user.Status,
		SlackWebhookUrl: user.SlackWebhookUrl,
		TelegramUserId:  user.TelegramUserId,
		Client:          user.Client,
		UserType:        user.UserType,
	}
	c.JSON(200, gin.H{
		"data": userResponse,
	})
}
func Profile(c *gin.Context) {
	token := strings.ReplaceAll(c.GetHeader("Authorization"), "Bearer ", "")
	claims, err := service.VerifyToken(token, "access")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := models.User{
		Id: uint(claims.UserId),
	}
	result := config.DB.Preload("Client").First(&user)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		c.JSON(400, gin.H{
			"message": "user not found",
		})
		return
	} else if result.Error != nil {
		c.JSON(400, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	userResponse := response.UserResponse{
		Id:              user.Id,
		Name:            user.Name,
		Email:           user.Email,
		Gender:          user.Gender,
		Description:     user.Description,
		CreatedAt:       utils.FormatTimeOrEmptyString(&user.CreatedAt),
		LastLoginDate:   utils.FormatTimeOrEmptyString(user.LastLoginDate),
		Status:          user.Status,
		SlackWebhookUrl: user.SlackWebhookUrl,
		TelegramUserId:  user.TelegramUserId,
		UserType:        user.UserType,
		Client:          user.Client,
	}
	c.JSON(200, gin.H{
		"data": userResponse,
	})
}
func CreateUser(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	hashedPassword := service.HashPassword(req.Password)
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)
	user := models.User{
		CreatedById:     userID,
		ClientId:        req.ClientId,
		UserType:        req.UserType,
		Name:            req.Name,
		Email:           req.Email,
		Password:        hashedPassword,
		Status:          req.Status,
		Gender:          req.Gender,
		TelegramUserId:  req.TelegramUserId,
		SlackWebhookUrl: req.SlackWebhookUrl,
		Description:     req.Description,
	}

	result := config.DB.Create(&user)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User created successfully"})
}
func UpdateUser(c *gin.Context) {
	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var user models.User
	userId, _ := strconv.Atoi(c.Param("id"))
	if err := config.DB.First(&user, userId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "User not found",
		})
		return
	}
	user.Name = req.Name
	user.Status = req.Status
	user.Email = req.Email
	user.Gender = req.Gender
	user.TelegramUserId = req.TelegramUserId
	user.SlackWebhookUrl = req.SlackWebhookUrl
	user.Description = req.Description
	user.UserType = req.UserType
	user.ClientId = req.ClientId
	if req.Password != "" {
		hashedPassword := service.HashPassword(req.Password)
		user.Password = hashedPassword
	}

	result := config.DB.Save(&user)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
}
func DeleteUser(c *gin.Context) {
	id := c.Param("id")
	var user models.User

	// Check if the user exists
	if err := config.DB.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		}
		return
	}

	// Delete the user
	if err := config.DB.Delete(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Could not delete user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}
func SearchUsers(c *gin.Context) {
	var users []models.User
	filter := map[string]interface{}{}
	if userType := c.Query("user_type"); userType != "" {
		filter["user_type"] = userType
	}

	query := config.DB.Where(filter).Preload("Client")
	if c.Query("s") != "" {
		query = query.Where("name like ? or id like ? or email like ?", "%"+c.Query("s")+"%", "%"+c.Query("s")+"%", "%"+c.Query("s")+"%")
	}
	query.Find(&users)
	var userResponses []response.UserResponse
	for _, user := range users {
		userResponse := response.UserResponse{
			Id:              user.Id,
			ClientId:        user.ClientId,
			Name:            user.Name,
			Email:           user.Email,
			Gender:          user.Gender,
			Description:     user.Description,
			CreatedAt:       utils.FormatTimeOrEmptyString(&user.CreatedAt),
			LastLoginDate:   utils.FormatTimeOrEmptyString(user.LastLoginDate),
			Status:          user.Status,
			SlackWebhookUrl: user.SlackWebhookUrl,
			TelegramUserId:  user.TelegramUserId,
			UserType:        user.UserType,
			Client:          user.Client,
		}
		userResponses = append(userResponses, userResponse)
	}
	c.JSON(http.StatusOK, gin.H{
		"data": userResponses,
	})
}
