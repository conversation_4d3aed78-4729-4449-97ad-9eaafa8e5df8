package middleware

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Query("token")
		if token == "" {
			token = strings.ReplaceAll(c.<PERSON>("Authorization"), "Bearer ", "")
		}

		claims, err := service.VerifyToken(token, "access")
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}
		var user models.User
		if err := config.DB.Preload("Client").First(&user, claims.UserId).Error; err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}
		c.<PERSON>("user", user)
		c.<PERSON>("userID", claims.UserId)
		c.Set("userType", claims.UserType)
		c.Next()
	}
}
