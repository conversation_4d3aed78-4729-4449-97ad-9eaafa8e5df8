package main

import (
	"fmt"
	"github.com/robfig/cron/v3"
	"log"
)

func main() {
	c := cron.New()
	c.AddFunc("* * * * *", func() { fmt.Println("Every minute cron here") })
	// Schedule the cron job to run every hour
	_, err := c.AddFunc("@hourly", func() { checkSpeedViolations() })
	if err != nil {
		log.Fatalf("Error adding cron job: %v", err)
	}

	// Start the cron scheduler
	c.Start()

	// Keep the program running
	select {}
}
func checkSpeedViolations() {

}
