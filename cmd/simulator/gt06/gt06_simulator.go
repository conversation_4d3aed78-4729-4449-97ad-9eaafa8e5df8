package main

import (
	"bufio"
	"encoding/hex"
	"fmt"
	"net"
	"time"
	"yotracker/internal/protocol/gt06"
)

func main() {
	for {
		// Attempt to establish a connection
		err := connectAndSimulate()
		if err != nil {
			fmt.Println("Connection error:", err)
			fmt.Println("Retrying in 5 seconds...")
			time.Sleep(5 * time.Second)
		}
	}
}
func simulateH02Protocol() {
	gpsData := []string{
		"2a48512c393137313030333430362c56352c3131333733392c412c313735342e363730342c532c333035372e303432372c452c302e30302c3231332c3233303632342c46464646464446462c3634382c34302c32313030352c31303931332c3023",
		"2a48512c393137313030333430362c56352c3035313032302c412c313735352e313035322c532c333035362e393236302c452c302e30302c302c3233303632342c46464646464446462c3634382c34302c32313031332c32363735322c3023",
		"2a48512c393137313030333430362c56352c3035303832322c412c313735352e313035322c532c333035362e393236302c452c302e30302c302c3233303632342c46464646464446462c3634382c34302c32313031332c32363735322c3023",
		"2a48512c393137313030333430362c56352c3035303632302c412c313735352e313035322c532c333035362e393236302c452c302e30302c302c3233303632342c46464646464446462c3634382c34302c32313031332c32363735322c3023",
		"2a48512c393137313030333430362c56352c3131343930312c412c313735352e313031382c532c333035362e383630312c452c302e30302c3231352c3233303632342c46464646464446462c3634382c34302c32313031332c32363735322c3023",
	}
	for {
		for _, v := range gpsData {
			// Send JSON data over TCP
			dat, _ := hex.DecodeString(v)
			err := sendDataOverTCP("localhost:5010", dat)
			if err != nil {
				fmt.Println("Error sending data:", err)
			}
			// Wait for 20 seconds before sending the next data
			time.Sleep(20 * time.Second)
		}
	}
}
func sendDataOverTCP(serverAddr string, data []byte) error {
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return fmt.Errorf("failed to connect to server: %v", err)
	}
	// Use a goroutine to listen for responses from the server
	//go listenForResponses(conn)
	// Simulate sending data periodically
	for {
		time.Sleep(5 * time.Second) // Wait before sending the next packet
		_, err = conn.Write(data)   // Resend the same GPS data or new data as needed
		if err != nil {
			return fmt.Errorf("failed to write data: %v", err)
		}
		fmt.Println("Resent GPS data:", data)
	}
}

// Function to listen for responses from the server
func listenForResponses(conn net.Conn, disconnect chan<- error) {
	reader := bufio.NewReader(conn)
	for {
		data, err := reader.ReadBytes(0x0A)
		if err != nil {
			disconnect <- err
			fmt.Println("Error reading data:", err.Error())
		}
		fmt.Println("Received response from server:", data, string(data))
	}
}
func connectAndSimulate() error {
	// Connect to the server
	serverAddress := "ignition.co.zw:5022"
	serverAddress = "localhost:5022"
	conn, err := net.Dial("tcp", serverAddress)
	if err != nil {
		return fmt.Errorf("failed to connect: %v", err)
	}
	defer conn.Close()

	fmt.Println("Connected to server")
	loginPacket := []byte{
		0x78, 0x78, 0x0D, 0x01, 0x03, 0x52, 0x67, 0x21, 0x08, 0x14, 0x55, 0x74, 0x04, 0xE4, 0xDC, 0x07, 0x0D, 0x0A,
	}

	fmt.Printf("Raw login packet: % X\n", loginPacket)
	fmt.Println("Sending login packet...", loginPacket)
	_, err = conn.Write(loginPacket)
	if err != nil {
		return fmt.Errorf("error sending login packet: %v", err)
	}

	fmt.Println("Login successful. Starting simulators...")

	// Channels to handle reconnection triggers
	disconnect := make(chan error)

	// Goroutines for periodic packets
	go listenForResponses(conn, disconnect)
	go sendHeartbeat(conn, disconnect)
	go sendLocation(conn, disconnect)
	go sendAlarm(conn, disconnect)

	// Wait for a disconnect trigger
	return <-disconnect

}
func sendLocation(conn net.Conn, disconnect chan<- error) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	packet := []byte{0x78, 0x78, 0x22, 0x22, 0x19, 0x03, 0x13, 0x0D, 0x38, 0x0F, 0xC6, 0x01, 0xEA, 0x58, 0x37, 0x03, 0x54, 0xF6, 0x3C, 0x00, 0x10, 0x4E, 0x02, 0x88, 0x04, 0x5A, 0x3E, 0x00, 0x3B, 0x1C, 0x00, 0x00, 0x00, 0x04, 0xE6, 0x43, 0x63, 0x0D, 0x0A}
	for range ticker.C {
		fmt.Println("Sending location...", packet)
		_, err := (conn).Write(packet)
		if err != nil {
			disconnect <- err
			return
		}
		fmt.Println("Location data sent")
	}
}
func sendHeartbeat(conn net.Conn, disconnect chan<- error) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	heartbeatPacket := []byte{0x78, 0x78, 0x08, 0x13, 0x4B, 0x04, 0x03, 0x00, 0x01, 0x00, 0x01}
	crc := gt06.CRC16_ITU(heartbeatPacket[2:])
	heartbeatPacket = append(heartbeatPacket, byte(crc>>8), byte(crc&0xFF), 0x0D, 0x0A)
	for range ticker.C {
		fmt.Println("Heartbeat data sent", heartbeatPacket)
		_, err := (conn).Write(heartbeatPacket)
		if err != nil {
			disconnect <- err
			return
		}
		fmt.Println("Heartbeat sent")
	}
}
func sendAlarm(conn net.Conn, disconnect chan<- error) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	// Tow alarm packet
	alarmPacket := []byte{
		0x78, 0x78, 0x25, 0x16, 0x19, 0x04, 0x1A, 0x0B, 0x12, 0x20, 0xCF,
		0x01, 0xEC, 0x24, 0xC6, 0x03, 0x52, 0x06, 0xFE, 0x00, 0x10, 0x00,
		0x09, 0x02, 0x88, 0x04, 0x52, 0x15, 0x00, 0x2D, 0x49, 0x65, 0x06,
		0x04, 0x01, 0x01, 0x00, 0x1D, 0xCC, 0x0D, 0x0A,
	}

	for range ticker.C {
		_, err := (conn).Write(alarmPacket)
		if err != nil {
			disconnect <- err
			return
		}
		fmt.Println("Alarm sent")
	}
}
