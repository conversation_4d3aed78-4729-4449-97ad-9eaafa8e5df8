package main

import (
	"bufio"
	"fmt"
	"net"
	"strings"
	"time"
)

func main() {
	fmt.Println("Starting h02 Simulator")
	for {
		err := connectAndSimulate()
		if err != nil {
			fmt.Println("Connection Error:", err.Error())
			fmt.Println("Retrying in 5 seconds...")
			time.Sleep(5 * time.Second)
		}
	}
}
func connectAndSimulate() (err error) {
	serverAddress := "localhost:5010"
	conn, err := net.Dial("tcp", serverAddress)
	if err != nil {
		return fmt.Errorf("failed to connect to server: %v", err)
	}
	defer conn.Close()
	disconnect := make(chan error)
	go sendLocationPacket(conn, disconnect)
	go sendAlarmPacket(conn, disconnect)
	go sendHeartbeatPacket(conn, disconnect)
	go listenForResponses(conn, disconnect)
	return <-disconnect
}
func listenForResponses(conn net.Conn, disconnect chan<- error) {

	reader := bufio.NewReader(conn)
	for {

		data, err := reader.ReadBytes('#')
		if err != nil {
			fmt.Println("Error reading from server:", err)
			disconnect <- err
			return
		}
		parts := strings.Split(string(data), ",")
		if len(parts) > 2 {
			if parts[2] == "S33" {
				fmt.Println("Received set speed alert command")
				//acknowledge the command
				command := fmt.Sprintf("*HQ,%s,V4,%s, speed %s", parts[1], parts[2], parts[3])
				_, err = conn.Write([]byte(command))
				if err != nil {
					fmt.Println("Error writing to server:", err.Error())
				}

			}
		}
		fmt.Println("Received data:", data)
		fmt.Println("Received data as a string:", string(data))
	}
}
func sendLocationPacket(conn net.Conn, disconnect chan<- error) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	packet := []byte("*HQ,9171003406,V5,175144,A,1755.1064,S,3056.9219,E,0.00,0,210325,FFFFFDFF,648,40,21005,10913,0#")
	binaryPacket := []byte{
		36, 145, 113, 0, 52, 6, 0, 3, 20, 34, 3, 37, 23, 85, 16, 66, 0, 3, 5, 105, 24, 10, 0, 0, 0, 251, 255, 253, 255, 0, 79, 80, 132, 0, 0, 0, 0, 2, 136, 40, 82, 21, 104, 127,
		36, 145, 113, 0, 52, 6, 0, 3, 20, 34, 3, 37, 23, 85, 16, 66, 0, 3, 5, 105, 24, 10, 0, 0, 0, 251, 255, 253, 255, 0, 79, 80, 132, 0, 0, 0, 0, 2, 136, 40, 82, 21, 104, 127,
		37, 36, 145, 113, 0, 52, 6, 0, 3, 20, 34, 3, 37, 23, 85, 16, 66, 0, 3, 5, 105, 24, 10, 0, 0, 0, 251, 255, 253, 255, 0, 79, 80, 132, 0, 0, 0, 0, 2, 136, 40, 82, 21, 104, 127,
		38, 36, 145, 113, 0, 52, 6, 0, 3, 20, 34, 3, 37, 23, 85, 16, 66, 0, 3, 5, 105, 24, 10, 0, 0, 0, 251, 255, 253, 255, 0, 79, 80, 132, 0, 0, 0, 0, 2, 136, 40, 82, 21, 104, 127,
		39, 36, 145, 113, 0, 52, 6, 0, 3, 20, 34, 3, 37, 23, 85, 16, 66, 0, 3, 5, 105, 24, 10, 0, 0, 0, 251, 255, 253, 255, 0, 79, 80, 132, 0, 0, 0, 0, 2, 136, 40, 82, 21, 104, 127,
		74, 36, 145, 113, 0, 52, 6, 17, 41, 4, 35, // Final bytes
	}
	fmt.Println("Binary packet length:", len(binaryPacket))
	count := 0
	var packetToSend []byte
	for range ticker.C {
		//alternate sending binaryPacket or packet
		if count%2 == 0 {
			packetToSend = packet
			fmt.Println("Sending string packet", packetToSend)
		} else {
			packetToSend = packet
			fmt.Println("Sending binary packet", packetToSend)
		}

		_, err := conn.Write(packetToSend)
		if err != nil {
			disconnect <- err
			return
		}
		count++
		fmt.Println("Sent location packet")
	}
}
func sendAlarmPacket(conn net.Conn, disconnect chan<- error) {

}
func sendHeartbeatPacket(conn net.Conn, disconnect chan<- error) {

}
