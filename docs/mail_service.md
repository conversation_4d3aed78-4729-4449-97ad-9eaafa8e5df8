# Mail Service Documentation

This document describes how to use the SendGrid-based mail service in YoTracker.

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```env
SENDGRID_API_KEY=your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>
FROM_NAME=YoTracker
```

### 2. SendGrid API Key

1. Sign up for a SendGrid account at https://sendgrid.com/
2. Create an API key with "Mail Send" permissions
3. Add the API key to your `.env` file

## Usage

### Quick Start with Helper Functions

For simple email sending, use the helper functions in `internal/mail/helper.go`:

```go
import "yotracker/internal/mail"

// Send a simple email
err := mail.SendEmail(
    []string{"<EMAIL>"},
    "Welcome to YoTracker",
    "Thank you for joining!",
    false, // isHTML
)

// Send HTML email with CC
err := mail.SendEmailWithCC(
    []string{"<EMAIL>"},
    []string{"<EMAIL>"},
    "Project Update",
    "<h2>Status Update</h2><p>Project is on track.</p>",
    true, // isHTML
)

// Send welcome email (predefined template)
err := mail.SendWelcomeEmail("<EMAIL>", "John Doe")

// Send device alert
err := mail.SendDeviceAlert("<EMAIL>", "DEV001", "Low Battery", "Battery level is below 10%")
```

### Advanced Usage with Mail Service

For more control, use the mail service directly:

```go
import (
    "yotracker/internal/service"
    "yotracker/internal/models"
)

// Initialize the service
mailService, err := service.NewMailService()
if err != nil {
    log.Fatal(err)
}

// Create a complex email
mail := models.NewMail(
    []string{"<EMAIL>", "<EMAIL>"},
    "Monthly Report",
)
mail.CC = []string{"<EMAIL>"}
mail.BCC = []string{"<EMAIL>"}
mail.ReplyTo = "<EMAIL>"

// Set both HTML and text content
mail.SetHTMLBody("<h2>Report</h2><p>See attachment.</p>")
mail.SetTextBody("Report\n\nSee attachment.")

// Add attachment
csvContent := []byte("Device,Status\nDEV001,Active")
mail.AddAttachment("report.csv", csvContent, "text/csv")

// Send the email
err = mailService.SendMail(mail)
```

## Available Helper Functions

### Basic Email Functions

- `SendEmail(to, subject, message, isHTML)` - Send simple email
- `SendEmailWithCC(to, cc, subject, message, isHTML)` - Send email with CC
- `SendEmailWithAttachment(to, subject, message, isHTML, filename, content, contentType)` - Send email with attachment

### Template Functions

- `SendWelcomeEmail(to, name)` - Send welcome email to new users
- `SendPasswordResetEmail(to, name, resetLink)` - Send password reset email
- `SendNotificationEmail(userEmail, userName, message)` - Send general notification
- `SendDeviceAlert(userEmail, deviceId, alertType, alertMessage)` - Send device alert
- `SendInvoiceEmail(userEmail, userName, invoiceNumber, pdfContent)` - Send invoice with PDF

### Bulk Email Functions

- `SendBulkEmail(recipients, subject, message, isHTML)` - Send to multiple recipients (they see each other)
- `SendBulkEmailIndividual(recipients, subject, message, isHTML)` - Send individual emails

## Mail Service Methods

### Core Methods

- `NewMailService()` - Create new mail service instance
- `SendMail(mail)` - Send a mail object
- `SendSimpleMail(to, subject, message, isHTML)` - Send simple email
- `SendMailWithCC(to, cc, subject, message, isHTML)` - Send with CC
- `SendMailWithAttachment(to, subject, message, isHTML, filename, content, contentType)` - Send with attachment

### Template Methods

- `SendWelcomeEmail(to, name)` - Welcome email template
- `SendPasswordResetEmail(to, name, resetLink)` - Password reset template

## Mail Model

### Creating Mail Objects

```go
// Simple mail
mail := models.NewMail([]string{"<EMAIL>"}, "Subject")

// From request (useful for API endpoints)
mailRequest := &models.MailRequest{
    To:      []string{"<EMAIL>"},
    Subject: "Subject",
    Message: "Message content",
    IsHTML:  true,
}
mail := models.NewMailFromRequest(mailRequest)
```

### Mail Properties

```go
type Mail struct {
    To          []string     // Required: Recipients
    CC          []string     // Optional: CC recipients
    BCC         []string     // Optional: BCC recipients
    ReplyTo     string       // Optional: Reply-to address
    Subject     string       // Required: Email subject
    HTMLBody    string       // Optional: HTML content
    TextBody    string       // Optional: Plain text content
    Attachments []Attachment // Optional: File attachments
}
```

### Adding Content and Attachments

```go
// Set content
mail.SetHTMLBody("<h1>HTML Content</h1>")
mail.SetTextBody("Plain text content")

// Add attachment
fileContent := []byte("file content")
mail.AddAttachment("filename.txt", fileContent, "text/plain")
```

## Error Handling

All mail functions return errors. Always check for errors:

```go
err := utils.SendEmail(to, subject, message, false)
if err != nil {
    log.Printf("Failed to send email: %v", err)
    // Handle error appropriately
}
```

Common errors:
- Missing environment variables
- Invalid email addresses
- SendGrid API errors
- Network connectivity issues

## Testing

Run the mail service tests:

```bash
go test ./internal/service -v
```

The tests cover:
- Mail service initialization
- Email validation
- Mail object creation and manipulation
- Error handling

## Best Practices

1. **Environment Variables**: Always set required environment variables
2. **Error Handling**: Always check for and handle errors
3. **Email Validation**: The service validates email addresses automatically
4. **Content**: Provide both HTML and text versions when possible
5. **Attachments**: Use appropriate MIME types for attachments
6. **Templates**: Use predefined templates for common email types
7. **Bulk Emails**: Use individual sending for personalized emails

## Integration Examples

### In Controllers

```go
func (ctrl *UserController) CreateUser(c *gin.Context) {
    // ... create user logic ...

    // Send welcome email
    err := utils.SendWelcomeEmail(user.Email, user.Name)
    if err != nil {
        log.Printf("Failed to send welcome email: %v", err)
        // Don't fail the request, just log the error
    }

    c.JSON(200, gin.H{"message": "User created successfully"})
}
```

### In Background Jobs

```go
func ProcessDeviceAlerts() {
    // ... get alerts ...

    for _, alert := range alerts {
        err := utils.SendDeviceAlert(
            alert.UserEmail,
            alert.DeviceId,
            alert.AlertType,
            alert.Message,
        )
        if err != nil {
            log.Printf("Failed to send alert email: %v", err)
        }
    }
}
```

## Troubleshooting

1. **"SENDGRID_API_KEY environment variable is required"**
   - Ensure SENDGRID_API_KEY is set in your .env file

2. **"FROM_EMAIL environment variable is required"**
   - Ensure FROM_EMAIL is set in your .env file

3. **"invalid email address"**
   - Check that all email addresses are properly formatted

4. **SendGrid API errors**
   - Check your SendGrid account status and API key permissions
   - Verify your domain is properly configured in SendGrid

5. **Import cycle errors**
   - Use `internal/utils/mail_helper.go` functions instead of importing service directly in utils
