<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice {{.Reference}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            color: #333;
        }
        .header {
            background-color: #a8d5c4;
            padding: 20px;
            display: flex;
            justify-content: space-between;
        }
        .header .company, .header .logo {
            width: 45%;
        }
        .logo {
            text-align: right;
        }
        .section {
            margin-top: 20px;
        }
        .flex {
            display: flex;
            justify-content: space-between;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            border: 1px solid #ccc;
            padding: 8px;
        }
        .table th {
            background-color: #004a77;
            color: white;
        }
        .totals {
            width: 300px;
            float: right;
            margin-top: 20px;
        }
        .totals td {
            padding: 5px;
        }
        .thankyou {
            margin-top: 40px;
            font-size: 18px;
        }
        .payment-info {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            font-size: 14px;
        }
        .bold { font-weight: bold; }
    </style>
</head>
<body>

<div class="header">
    <div class="company">
        <h2>{{.Client.Name}}</h2>
        <p>{{.Client.Address}}</p>
        <p>{{.Client.City}}, {{.Client.State}}, {{.Client.Country}}</p>
        <p>{{.Client.Email}}</p>
    </div>
    <div class="logo">
        <img src="file:///app/public/static/logo.png" alt="Logo" height="80"/>
    </div>
</div>

<div class="section flex">
    <div>
        <h3>BILL TO</h3>
        <p>{{.BillTo.Name}}</p>
        <p>{{.BillTo.Company}}</p>
        <p>{{.BillTo.Address}}</p>
        <p>{{.BillTo.City}}, {{.BillTo.Country}}</p>
    </div>
    <div>
        <h3>INVOICE DETAILS</h3>
        <p><span class="bold">Invoice No:</span> {{.Reference}}</p>
        <p><span class="bold">Invoice Date:</span> {{.Date}}</p>
        <p><span class="bold">Due Date:</span> {{.DueDate}}</p>
        <p><span class="bold">Terms:</span> {{.Terms}}</p>
    </div>
</div>

<table class="table">
    <thead>
    <tr>
        <th>Description</th>
        <th>Qty</th>
        <th>Unit Price</th>
        <th>Amount</th>
    </tr>
    </thead>
    <tbody>
    {{range .InvoiceItems}}
    <tr>
        <td>{{.Description}}</td>
        <td>{{.Qty}}</td>
        <td>${{printf "%.2f" .UnitPrice}}</td>
        <td>${{printf "%.2f" .Total}}</td>
    </tr>
    {{end}}
    </tbody>
</table>

<table class="totals">
    <tr><td>Sub Total</td><td class="bold right">${{printf "%.2f" .Subtotal}}</td></tr>
    <tr><td>Tax</td><td class="bold right">${{printf "%.2f" .TaxAmount}}</td></tr>
    <tr><td>Total Due</td><td class="bold right">${{printf "%.2f" .Total}}</td></tr>
</table>

<div class="thankyou">
    Thank you for your business!
</div>

<div class="payment-info">
    <div>
        <h4>Payment by Mail:</h4>
        <p>{{.Client.Name}}</p>
        <p>{{.Client.Address}}</p>
        <p>{{.Client.City}}, {{.Client.Country}}</p>
    </div>
    <div>
        <h4>Payment by ACH:</h4>
        <p><span class="bold">Bank:</span> {{.BankName}}</p>
        <p><span class="bold">Routing #:</span> {{.BankRouting}}</p>
        <p><span class="bold">Account #:</span> {{.BankAccount}}</p>
    </div>
</div>

</body>
</html>