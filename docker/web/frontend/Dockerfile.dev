FROM golang:1.21-slim

WORKDIR /app/yotracker

# Install build tools and wkhtmltopdf dependencies
RUN apk add --no-cache \
    build-base \
    wkhtmltopdf \
    ttf-freefont \
    fontconfig

# Install Air for hot reloading
RUN go install github.com/cosmtrek/air@v1.49.0

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd ./cmd
COPY internal ./internal
COPY config ./config
COPY ./docker/web/frontend/.air.toml ./.air.toml
COPY .env ./

# Expose the backend port
EXPOSE ${BACKEND_PORT:-9000}

# Run Air for hot reloading
CMD ["air", "-c", "/app/yotracker/.air.toml"]