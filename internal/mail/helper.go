package mail

import (
	"fmt"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

// SendEmail sends a simple email using the mail service
func SendEmail(to []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendSimpleMail(to, subject, message, isHTML)
}

// SendEmailWithCC sends an email with CC recipients
func SendEmailWithCC(to []string, cc []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendMailWithCC(to, cc, subject, message, isHTML)
}

// SendEmailWithAttachment sends an email with an attachment
func SendEmailWithAttachment(to []string, subject, message string, isHTML bool, filename string, content []byte, contentType string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendMailWithAttachment(to, subject, message, isHTML, filename, content, contentType)
}

// SendWelcomeEmail sends a welcome email to new users
func SendWelcomeEmail(to string, name string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendWelcomeEmail(to, name)
}

// SendPasswordResetEmail sends a password reset email
func SendPasswordResetEmail(to string, name string, resetLink string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendPasswordResetEmail(to, name, resetLink)
}

// SendNotificationEmail sends a general notification email using template
func SendNotificationEmail(userEmail, userName, message string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendTemplatedEmail(userEmail, "YoTracker Notification", func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Hello %s,", userName)).
			AddContent(message)
	})
}

// SendDeviceAlert sends a device alert email
func SendDeviceAlert(userEmail, deviceId, alertType, alertMessage string) error {
	subject := fmt.Sprintf("Device Alert: %s - %s", deviceId, alertType)

	htmlMessage := fmt.Sprintf(`
		<html>
		<body>
			<h2>Device Alert</h2>
			<p><strong>Device ID:</strong> %s</p>
			<p><strong>Alert Type:</strong> %s</p>
			<p><strong>Message:</strong> %s</p>
			<p>Please check your device status in the YoTracker dashboard.</p>
		</body>
		</html>
	`, deviceId, alertType, alertMessage)

	return SendEmail([]string{userEmail}, subject, htmlMessage, true)
}

// SendInvoiceEmail sends an invoice email with attachment
func SendInvoiceEmail(userEmail, userName, invoiceNumber string, pdfContent []byte) error {
	subject := fmt.Sprintf("Invoice %s - YoTracker", invoiceNumber)

	htmlMessage := fmt.Sprintf(`
		<html>
		<body>
			<h2>Invoice %s</h2>
			<p>Dear %s,</p>
			<p>Please find your invoice attached.</p>
			<p>Thank you for your business!</p>
			<br>
			<p>Best regards,<br>The YoTracker Team</p>
		</body>
		</html>
	`, invoiceNumber, userName)

	filename := fmt.Sprintf("invoice_%s.pdf", invoiceNumber)
	return SendEmailWithAttachment([]string{userEmail}, subject, htmlMessage, true, filename, pdfContent, "application/pdf")
}

// SendBulkEmail sends the same email to multiple recipients
func SendBulkEmail(recipients []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Send to all recipients at once (they'll all see each other's emails)
	// If you want to send individual emails, use SendBulkEmailIndividual instead
	return mailService.SendSimpleMail(recipients, subject, message, isHTML)
}

// SendBulkEmailIndividual sends individual emails to multiple recipients
func SendBulkEmailIndividual(recipients []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Send individual emails to each recipient
	for _, recipient := range recipients {
		err := mailService.SendSimpleMail([]string{recipient}, subject, message, isHTML)
		if err != nil {
			return fmt.Errorf("failed to send email to %s: %v", recipient, err)
		}
	}

	return nil
}
