package mail

import (
	"os"
	"testing"
)

func TestSendEmailHelpers(t *testing.T) {
	// Set up test environment variables
	os.Setenv("SENDGRID_API_KEY", "test-key")
	os.Setenv("FROM_EMAIL", "<EMAIL>")
	os.Setenv("FROM_NAME", "Test Sender")

	// Test SendEmail function exists and can be called
	// Note: We're not actually sending emails in tests, just testing the function signature
	err := SendEmail([]string{"<EMAIL>"}, "Test Subject", "Test Message", false)
	// We expect this to fail with a SendGrid error since we're using a fake API key
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}

	// Test SendEmailWithCC function exists
	err = SendEmailWithCC(
		[]string{"<EMAIL>"},
		[]string{"<EMAIL>"},
		"Test Subject",
		"Test Message",
		false,
	)
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}

	// Test SendWelcomeEmail function exists
	err = SendWelcomeEmail("<EMAIL>", "Test User")
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}

	// Test SendPasswordResetEmail function exists
	err = SendPasswordResetEmail("<EMAIL>", "Test User", "http://example.com/reset")
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}

	// Test SendNotificationEmail function exists
	err = SendNotificationEmail("<EMAIL>", "Test User", "Test notification message")
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}

	// Test SendDeviceAlert function exists
	err = SendDeviceAlert("<EMAIL>", "DEV001", "Low Battery", "Battery is low")
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}

	// Test SendInvoiceEmail function exists
	pdfContent := []byte("fake pdf content")
	err = SendInvoiceEmail("<EMAIL>", "Test User", "INV001", pdfContent)
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}

	// Test SendBulkEmail function exists
	err = SendBulkEmail([]string{"<EMAIL>", "<EMAIL>"}, "Bulk Subject", "Bulk Message", false)
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}

	// Test SendBulkEmailIndividual function exists
	err = SendBulkEmailIndividual([]string{"<EMAIL>", "<EMAIL>"}, "Individual Subject", "Individual Message", false)
	if err == nil {
		t.Error("Expected error with fake API key, but got none")
	}
}

func TestHelperFunctionsWithoutEnvVars(t *testing.T) {
	// Clear environment variables
	os.Unsetenv("SENDGRID_API_KEY")
	os.Unsetenv("FROM_EMAIL")
	os.Unsetenv("FROM_NAME")

	// Test that functions return appropriate errors when env vars are missing
	err := SendEmail([]string{"<EMAIL>"}, "Test Subject", "Test Message", false)
	if err == nil {
		t.Error("Expected error when environment variables are missing")
	}

	if err != nil && err.Error() != "failed to initialize mail service: SENDGRID_API_KEY environment variable is required" {
		t.Errorf("Expected specific error message, got: %v", err)
	}
}
