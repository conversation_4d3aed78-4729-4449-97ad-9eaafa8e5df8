package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"math"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

func Paginate(c *gin.Context) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		page, _ := strconv.Atoi(c.Query("page"))
		if page <= 0 {
			page = 1
		}

		perPage, _ := strconv.Atoi(c.Query("per_page"))
		switch {
		case perPage > 100:
			perPage = 100
		case perPage <= 0:
			perPage = 10
		}

		offset := (page - 1) * perPage
		return db.Offset(offset).Limit(perPage)
	}
}
func GetValueOrEmptyString(s *string) string {
	if s != nil {
		return *s
	}
	return ""
}
func GetValueOrEmptyInt(s *uint) uint {
	if s != nil {
		return *s
	}
	return 0
}
func FormatTimeOrEmptyString(t *time.Time) string {
	if t != nil {
		return t.Format(time.RFC3339) // Or use your preferred format
	}
	return ""
}
func SetDeviceAsOnline(device *models.ClientDevice) {
	config.DB.Model(device).Updates(map[string]interface{}{
		"connected":     true,
		"device_status": "online",
	})
}
func SetDeviceAsOffline(device *models.ClientDevice) {
	config.DB.Model(device).Updates(map[string]interface{}{
		"connected":     false,
		"device_status": "offline",
	})
}
func FindDevice(deviceId string) (*models.ClientDevice, error) {
	var device models.ClientDevice
	result := config.DB.Where("device_id = ?", deviceId).First(&device)
	if result.Error != nil {
		fmt.Println("Error finding device:", result.Error)
		return nil, result.Error
	}
	return &device, nil
}
func GetDirection(degree int) string {
	directions := []string{"north", "north_east", "east", "south_east", "south", "south_west", "west", "north_west"}
	return directions[int((float64(degree)+22.5)/45)%8]

}

// UpdateDeviceStatus updates the device's voltage and signal strength information
func UpdateDeviceStatus(deviceId string, voltage float64, signalStrength float64) error {
	device, err := FindDevice(deviceId)
	if err != nil {
		return fmt.Errorf("error finding device: %v", err)
	}

	// Update the device with voltage and signal strength information
	updates := map[string]interface{}{
		"battery_level":      voltage,
		"signal_strength":    signalStrength,
		"last_status_update": time.Now(),
	}

	result := config.DB.Model(device).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("error updating device status: %v", result.Error)
	}

	return nil
}

func SendCommandViaSMS(command *models.CommandLog) error {
	return nil
}
func SendSMS(phoneNumber string, message string) {

}

// SendEmail sends an email using the mail service
func SendEmail(to []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendSimpleMail(to, subject, message, isHTML)
}

// SendEmailWithCC sends an email with CC recipients
func SendEmailWithCC(to []string, cc []string, subject, message string, isHTML bool) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendMailWithCC(to, cc, subject, message, isHTML)
}

// GenerateReference generates a reference string based on the provided ID and setting key
func GenerateReference(id string) string {

	// Get prefix from settings
	prefix := ""
	var setting models.Setting
	if err := config.DB.Where("setting_key = 'invoice_reference_prefix'").First(&setting).Error; err == nil {
		prefix = setting.SettingValue
	}

	// Format sequence number with leading zeros
	var sequenceNumber string
	if len(id) < 2 {
		sequenceNumber = "00" + id
	} else if len(id) < 3 {
		sequenceNumber = "0" + id
	} else {
		sequenceNumber = id
	}

	// Generate random number (equivalent to PHP's uniqid)
	randomNumber := generateUniqueID()

	// Get reference format from settings
	referenceFormat := "Sequence Number" // default
	var formatSetting models.Setting
	if err := config.DB.Where("setting_key = ?", "invoice_reference_format").First(&formatSetting).Error; err == nil {
		referenceFormat = formatSetting.SettingValue
	}

	// Apply the appropriate format
	switch referenceFormat {
	case "Sequence Number":
		return prefix + sequenceNumber
	case "Random Number":
		return prefix + randomNumber
	case "YEAR/Sequence Number (SL/2014/001)":
		return prefix + time.Now().Format("2006") + "/" + sequenceNumber
	case "YEAR/MONTH/Sequence Number (SL/2014/08/001)":
		return prefix + time.Now().Format("2006") + "/" + time.Now().Format("01") + "/" + sequenceNumber
	default:
		return id
	}
}

// generateUniqueID generates a unique identifier similar to PHP's uniqid()
func generateUniqueID() string {
	// Generate 8 random bytes
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to timestamp-based ID if random generation fails
		return fmt.Sprintf("%x", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// HaversineDistance calculates distance between two lat/lng points in KM
func HaversineDistance(lat1, lon1, lat2, lon2 float64) float64 {
	const R = 6371 // Radius of Earth in KM
	dLat := (lat2 - lat1) * math.Pi / 180.0
	dLon := (lon2 - lon1) * math.Pi / 180.0

	a := math.Sin(dLat/2)*math.Sin(dLat/2) +
		math.Cos(lat1*math.Pi/180.0)*math.Cos(lat2*math.Pi/180.0)*
			math.Sin(dLon/2)*math.Sin(dLon/2)

	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	return R * c
}
