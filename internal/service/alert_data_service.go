package service

import (
	"encoding/json"
	"fmt"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type Alert struct {
	DeviceId       string
	AlertType      string
	AlertName      string
	Message        string
	RawData        string
	AdditionalData json.RawMessage
	Speed          float64
	Direction      string
	Timestamp      time.Time
}

func (a *Alert) SaveAlert() bool {
	//check for client device id
	clientDevice := models.ClientDevice{DeviceId: a.DeviceId}
	config.DB.First(&clientDevice)
	if clientDevice.Id != 0 {
		alert := models.Alert{
			ClientDeviceId: clientDevice.Id,
			DeviceId:       &a.DeviceId,
			AlertType:      a.AlertType,
			AlertName:      &a.AlertName,
			Message:        &a.Message,
			RawData:        &a.RawData,
			AdditionalData: a.AdditionalData,
			Speed:          &a.Speed,
			Direction:      &a.Direction,
			AlertTimestamp: a.Timestamp,
		}
		result := config.DB.Create(&alert)
		if result.Error != nil {
			return false
		}
		fmt.Println("Successfully saved")
	} else {
		fmt.Println("Could not find client")
	}
	return false
}
