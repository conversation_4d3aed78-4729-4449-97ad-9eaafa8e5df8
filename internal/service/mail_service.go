package service

import (
	"encoding/base64"
	"errors"
	"fmt"
	"log"
	"os"

	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
	"yotracker/internal/models"
)

// MailService handles email operations using SendGrid
type MailService struct {
	client   *sendgrid.Client
	fromName string
	fromEmail string
}

// NewMailService creates a new instance of MailService
func NewMailService() (*MailService, error) {
	apiKey := os.Getenv("SENDGRID_API_KEY")
	if apiKey == "" {
		return nil, errors.New("SENDGRID_API_KEY environment variable is required")
	}

	fromEmail := os.Getenv("FROM_EMAIL")
	if fromEmail == "" {
		return nil, errors.New("FROM_EMAIL environment variable is required")
	}

	fromName := os.Getenv("FROM_NAME")
	if fromName == "" {
		fromName = "YoTracker" // Default name
	}

	client := sendgrid.NewSendClient(apiKey)

	return &MailService{
		client:    client,
		fromName:  fromName,
		fromEmail: fromEmail,
	}, nil
}

// SendMail sends an email using SendGrid
func (ms *MailService) SendMail(mailData *models.Mail) error {
	// Validate the mail data
	if err := mailData.Validate(); err != nil {
		return fmt.Errorf("mail validation failed: %w", err)
	}

	// Create the SendGrid message
	message := ms.buildSendGridMessage(mailData)

	// Send the email
	response, err := ms.client.Send(message)
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	// Check response status
	if response.StatusCode >= 400 {
		return fmt.Errorf("sendgrid returned error status %d: %s", response.StatusCode, response.Body)
	}

	log.Printf("Email sent successfully. Status: %d", response.StatusCode)
	return nil
}

// buildSendGridMessage constructs a SendGrid mail message from our Mail model
func (ms *MailService) buildSendGridMessage(mailData *models.Mail) *mail.SGMailV3 {
	// Create from address
	from := mail.NewEmail(ms.fromName, ms.fromEmail)

	// Create the message
	message := mail.NewV3Mail()
	message.SetFrom(from)
	message.Subject = mailData.Subject

	// Set reply-to if provided
	if mailData.ReplyTo != "" {
		replyTo := mail.NewEmail("", mailData.ReplyTo)
		message.SetReplyTo(replyTo)
	}

	// Create personalization (recipients)
	personalization := mail.NewPersonalization()

	// Add TO recipients
	for _, toEmail := range mailData.To {
		personalization.AddTos(mail.NewEmail("", toEmail))
	}

	// Add CC recipients
	for _, ccEmail := range mailData.CC {
		personalization.AddCCs(mail.NewEmail("", ccEmail))
	}

	// Add BCC recipients
	for _, bccEmail := range mailData.BCC {
		personalization.AddBCCs(mail.NewEmail("", bccEmail))
	}

	message.AddPersonalizations(personalization)

	// Set content
	if mailData.TextBody != "" {
		message.AddContent(mail.NewContent("text/plain", mailData.TextBody))
	}

	if mailData.HTMLBody != "" {
		message.AddContent(mail.NewContent("text/html", mailData.HTMLBody))
	}

	// Add attachments
	for _, attachment := range mailData.Attachments {
		sgAttachment := mail.NewAttachment()
		sgAttachment.SetFilename(attachment.Filename)
		sgAttachment.SetContent(base64.StdEncoding.EncodeToString(attachment.Content))
		
		if attachment.ContentType != "" {
			sgAttachment.SetType(attachment.ContentType)
		}
		
		message.AddAttachment(sgAttachment)
	}

	return message
}

// SendSimpleMail sends a simple email with basic parameters
func (ms *MailService) SendSimpleMail(to []string, subject, message string, isHTML bool) error {
	mail := models.NewMail(to, subject)
	
	if isHTML {
		mail.SetHTMLBody(message)
	} else {
		mail.SetTextBody(message)
	}

	return ms.SendMail(mail)
}

// SendMailWithCC sends an email with CC recipients
func (ms *MailService) SendMailWithCC(to []string, cc []string, subject, message string, isHTML bool) error {
	mail := models.NewMail(to, subject)
	mail.CC = cc
	
	if isHTML {
		mail.SetHTMLBody(message)
	} else {
		mail.SetTextBody(message)
	}

	return ms.SendMail(mail)
}

// SendMailWithAttachment sends an email with an attachment
func (ms *MailService) SendMailWithAttachment(to []string, subject, message string, isHTML bool, filename string, content []byte, contentType string) error {
	mail := models.NewMail(to, subject)
	
	if isHTML {
		mail.SetHTMLBody(message)
	} else {
		mail.SetTextBody(message)
	}

	mail.AddAttachment(filename, content, contentType)

	return ms.SendMail(mail)
}

// SendWelcomeEmail sends a welcome email to new users
func (ms *MailService) SendWelcomeEmail(to string, name string) error {
	subject := "Welcome to YoTracker!"
	htmlBody := fmt.Sprintf(`
		<html>
		<body>
			<h2>Welcome to YoTracker, %s!</h2>
			<p>Thank you for joining YoTracker. We're excited to have you on board.</p>
			<p>You can now start tracking your devices and managing your fleet.</p>
			<p>If you have any questions, please don't hesitate to contact our support team.</p>
			<br>
			<p>Best regards,<br>The YoTracker Team</p>
		</body>
		</html>
	`, name)

	textBody := fmt.Sprintf(`
Welcome to YoTracker, %s!

Thank you for joining YoTracker. We're excited to have you on board.

You can now start tracking your devices and managing your fleet.

If you have any questions, please don't hesitate to contact our support team.

Best regards,
The YoTracker Team
	`, name)

	mail := models.NewMail([]string{to}, subject)
	mail.SetHTMLBody(htmlBody)
	mail.SetTextBody(textBody)

	return ms.SendMail(mail)
}

// SendPasswordResetEmail sends a password reset email
func (ms *MailService) SendPasswordResetEmail(to string, name string, resetLink string) error {
	subject := "Password Reset Request - YoTracker"
	htmlBody := fmt.Sprintf(`
		<html>
		<body>
			<h2>Password Reset Request</h2>
			<p>Hello %s,</p>
			<p>We received a request to reset your password for your YoTracker account.</p>
			<p>Click the link below to reset your password:</p>
			<p><a href="%s">Reset Password</a></p>
			<p>If you didn't request this password reset, please ignore this email.</p>
			<p>This link will expire in 24 hours.</p>
			<br>
			<p>Best regards,<br>The YoTracker Team</p>
		</body>
		</html>
	`, name, resetLink)

	textBody := fmt.Sprintf(`
Password Reset Request

Hello %s,

We received a request to reset your password for your YoTracker account.

Click the link below to reset your password:
%s

If you didn't request this password reset, please ignore this email.

This link will expire in 24 hours.

Best regards,
The YoTracker Team
	`, name, resetLink)

	mail := models.NewMail([]string{to}, subject)
	mail.SetHTMLBody(htmlBody)
	mail.SetTextBody(textBody)

	return ms.SendMail(mail)
}
