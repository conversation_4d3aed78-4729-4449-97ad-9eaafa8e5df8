package models

type Currency struct {
	Id                uint    `json:"id"`
	Name              string  `json:"name"`
	Code              string  `json:"code"`
	Symbol            string  `json:"symbol"`
	Decimals          float32 `json:"decimals" gorm:"default:2"`
	Xrate             float32 `json:"xrate"`
	InternationalCode string  `json:"international_code"`
	Active            bool    `json:"active" gorm:"default:true"`
	SymbolPosition    string  `json:"symbol_position" gorm:"default:'left'"`
}
type CurrencyRequest struct {
	Name              string  `json:"name" binding:"required"`
	Code              string  `json:"code" binding:"required"`
	Symbol            string  `json:"symbol" binding:"required"`
	Decimals          float32 `json:"decimals"`
	Xrate             float32 `json:"xrate"`
	InternationalCode string  `json:"international_code"`
	Active            bool    `json:"active"`
	SymbolPosition    string  `json:"symbol_position"`
}
