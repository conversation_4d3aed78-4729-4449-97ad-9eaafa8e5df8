package models

import (
	"encoding/json"
	"time"
)

type Fleet struct {
	Id           uint             `json:"id" gorm:"primaryKey"`
	ParentId     *uint            `json:"parent_id,omitempty"`
	ClientId     uint             `json:"client_id"`
	Name         string           `json:"name"`
	Description  *string          `json:"description" gorm:"type:text"`
	Emails       *json.RawMessage `json:"emails" gorm:"type:json"`
	PhoneNumbers *json.RawMessage `json:"phone_numbers" gorm:"type:json"`
	CreatedAt    time.Time        `json:"created_at"`
	UpdatedAt    time.Time        `json:"updated_at"`
}
type FleetRequest struct {
	ParentId     *uint            `json:"parent_id,omitempty"`
	Name         string           `json:"name" binding:"required"`
	Description  *string          `json:"description,omitempty"`
	Emails       *json.RawMessage `json:"emails,omitempty"`
	PhoneNumbers *json.RawMessage `json:"phone_numbers,omitempty"`
}
