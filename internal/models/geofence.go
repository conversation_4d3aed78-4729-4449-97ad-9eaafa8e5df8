package models

type Geofence struct {
	Id             uint    `json:"id" gorm:"primaryKey"`
	ClientDeviceId *uint   `json:"client_device_id" gorm:"index"`
	DeviceId       string  `json:"device_id"`
	Name           string  `json:"name"`
	Radius         float64 `json:"radius"`
	Latitude       float64 `json:"latitude"`
	Longitude      float64 `json:"longitude"`
	Status         string  `json:"status" gorm:"default:'active'"`
	CreatedAt      string  `json:"created_at"`
	UpdatedAt      string  `json:"updated_at"`
}
