package main

import (
	"fmt"
	"log"
	"yotracker/internal/models"
	"yotracker/internal/service"
)

func main() {
	// Initialize mail service once
	mailService, err := service.NewMailService()
	if err != nil {
		log.Printf("Failed to create mail service: %v", err)
		return
	}

	// Example 1: Simple email
	fmt.Println("Example 1: Simple email")
	err = mailService.SendSimpleMail(
		[]string{"<EMAIL>"},
		"Welcome to YoTracker",
		"Thank you for joining YoTracker!",
		false, // isHTML
	)
	if err != nil {
		log.Printf("Failed to send simple email: %v", err)
	} else {
		fmt.Println("Simple email sent successfully!")
	}

	// Example 2: Email with CC
	fmt.Println("\nExample 2: Email with CC")
	err = mailService.SendMailWithCC(
		[]string{"<EMAIL>"},
		[]string{"<EMAIL>"},
		"Project Update",
		"<h2>Project Status Update</h2><p>The project is progressing well.</p>",
		true, // isHTML
	)
	if err != nil {
		log.Printf("Failed to send email with CC: %v", err)
	} else {
		fmt.Println("Email with CC sent successfully!")
	}

	// Example 3: Using the mail service directly for more control
	fmt.Println("\nExample 3: Using mail service directly")

	// Create a mail with multiple recipients and attachments
	mail := models.NewMail(
		[]string{"<EMAIL>", "<EMAIL>"},
		"Monthly Report",
	)
	mail.CC = []string{"<EMAIL>"}
	mail.BCC = []string{"<EMAIL>"}
	mail.ReplyTo = "<EMAIL>"

	// Set both HTML and text content
	mail.SetHTMLBody(`
		<html>
		<body>
			<h2>Monthly Report</h2>
			<p>Please find the monthly report attached.</p>
			<ul>
				<li>Total devices: 150</li>
				<li>Active devices: 142</li>
				<li>Alerts generated: 23</li>
			</ul>
		</body>
		</html>
	`)

	mail.SetTextBody(`
Monthly Report

Please find the monthly report attached.

- Total devices: 150
- Active devices: 142
- Alerts generated: 23
	`)

	// Add an attachment (example CSV content)
	csvContent := []byte("Device ID,Status,Last Seen\nDEV001,Active,2024-01-15\nDEV002,Inactive,2024-01-14")
	mail.AddAttachment("monthly_report.csv", csvContent, "text/csv")

	err = mailService.SendMail(mail)
	if err != nil {
		log.Printf("Failed to send detailed email: %v", err)
	} else {
		fmt.Println("Detailed email sent successfully!")
	}

	// Example 4: Using predefined templates
	fmt.Println("\nExample 4: Using predefined welcome email template")
	err = mailService.SendWelcomeEmail("<EMAIL>", "John Doe")
	if err != nil {
		log.Printf("Failed to send welcome email: %v", err)
	} else {
		fmt.Println("Welcome email sent successfully!")
	}

	// Example 5: Using password reset template
	fmt.Println("\nExample 5: Using password reset email template")
	resetLink := "https://yourdomain.com/reset-password?token=abc123"
	err = mailService.SendPasswordResetEmail("<EMAIL>", "Jane Smith", resetLink)
	if err != nil {
		log.Printf("Failed to send password reset email: %v", err)
	} else {
		fmt.Println("Password reset email sent successfully!")
	}

	// Example 6: Creating mail from request (useful for API endpoints)
	fmt.Println("\nExample 6: Creating mail from request structure")
	mailRequest := &models.MailRequest{
		To:      []string{"<EMAIL>"},
		CC:      []string{"<EMAIL>"},
		Subject: "Order Confirmation",
		Message: "<h2>Order Confirmed</h2><p>Your order #12345 has been confirmed.</p>",
		IsHTML:  true,
	}

	mailFromRequest := models.NewMailFromRequest(mailRequest)
	err = mailService.SendMail(mailFromRequest)
	if err != nil {
		log.Printf("Failed to send mail from request: %v", err)
	} else {
		fmt.Println("Mail from request sent successfully!")
	}

	// Example 7: Error handling and validation
	fmt.Println("\nExample 7: Error handling and validation")
	invalidMail := &models.Mail{
		To:      []string{"invalid-email"},
		Subject: "Test",
		TextBody: "Test message",
	}

	err = mailService.SendMail(invalidMail)
	if err != nil {
		fmt.Printf("Expected validation error: %v\n", err)
	}

	fmt.Println("\nAll examples completed!")
}

// Example function that could be used in your controllers
func SendNotificationEmail(userEmail, userName, message string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	mail := models.NewMail([]string{userEmail}, "YoTracker Notification")
	mail.SetHTMLBody(fmt.Sprintf(`
		<html>
		<body>
			<h2>Hello %s,</h2>
			<p>%s</p>
			<br>
			<p>Best regards,<br>The YoTracker Team</p>
		</body>
		</html>
	`, userName, message))

	mail.SetTextBody(fmt.Sprintf(`
Hello %s,

%s

Best regards,
The YoTracker Team
	`, userName, message))

	return mailService.SendMail(mail)
}

// Example function for sending device alerts
func SendDeviceAlert(userEmail, deviceId, alertType, alertMessage string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	subject := fmt.Sprintf("Device Alert: %s - %s", deviceId, alertType)

	htmlMessage := fmt.Sprintf(`
		<html>
		<body>
			<h2>Device Alert</h2>
			<p><strong>Device ID:</strong> %s</p>
			<p><strong>Alert Type:</strong> %s</p>
			<p><strong>Message:</strong> %s</p>
			<p>Please check your device status in the YoTracker dashboard.</p>
		</body>
		</html>
	`, deviceId, alertType, alertMessage)

	return mailService.SendSimpleMail([]string{userEmail}, subject, htmlMessage, true)
}
