package main

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/internal/mail"
	"yotracker/internal/models"
)

// Example of how to integrate mail functionality into your controllers

// UserController example showing mail integration
type UserController struct{}

// <PERSON><PERSON><PERSON><PERSON> creates a new user and sends a welcome email
func (ctrl *UserController) CreateUser(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create user logic here...
	// user := createUserInDatabase(req)

	// Send welcome email (non-blocking)
	go func() {
		err := mail.SendWelcomeEmail(req.Email, req.Name)
		if err != nil {
			// Log error but don't fail the request
			// log.Printf("Failed to send welcome email to %s: %v", req.Email, err)
		}
	}()

	c.<PERSON>(http.StatusCreated, gin.H{
		"message": "User created successfully",
		"email":   "Welcome email will be sent shortly",
	})
}

// RequestPasswordReset sends a password reset email
func (ctrl *UserController) RequestPasswordReset(c *gin.Context) {
	var req struct {
		Email string `json:"email" binding:"required,email"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Find user and generate reset token logic here...
	// user := findUserByEmail(req.Email)
	// resetToken := generateResetToken(user)
	// resetLink := fmt.Sprintf("https://yourdomain.com/reset-password?token=%s", resetToken)

	// For demo purposes
	resetLink := "https://yourdomain.com/reset-password?token=demo-token"
	userName := "Demo User"

	// Send password reset email
	err := mail.SendPasswordResetEmail(req.Email, userName, resetLink)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to send password reset email",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Password reset email sent successfully",
	})
}

// DeviceController example showing device alert emails
type DeviceController struct{}

// SendDeviceAlert sends an alert email for a specific device
func (ctrl *DeviceController) SendDeviceAlert(c *gin.Context) {
	var req struct {
		DeviceId     string `json:"device_id" binding:"required"`
		UserEmail    string `json:"user_email" binding:"required,email"`
		AlertType    string `json:"alert_type" binding:"required"`
		AlertMessage string `json:"alert_message" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Send device alert email
	err := mail.SendDeviceAlert(req.UserEmail, req.DeviceId, req.AlertType, req.AlertMessage)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to send device alert email",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Device alert email sent successfully",
	})
}

// NotificationController example for general notifications
type NotificationController struct{}

// SendNotification sends a general notification email
func (ctrl *NotificationController) SendNotification(c *gin.Context) {
	var req struct {
		UserEmail string `json:"user_email" binding:"required,email"`
		UserName  string `json:"user_name" binding:"required"`
		Message   string `json:"message" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Send notification email
	err := mail.SendNotificationEmail(req.UserEmail, req.UserName, req.Message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to send notification email",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Notification email sent successfully",
	})
}

// MailController for direct mail operations
type MailController struct{}

// SendCustomEmail sends a custom email with full control
func (ctrl *MailController) SendCustomEmail(c *gin.Context) {
	var req models.MailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Send email using helper function
	err := mail.SendEmail(req.To, req.Subject, req.Message, req.IsHTML)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to send email",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Email sent successfully",
	})
}

// SendEmailWithCC sends an email with CC recipients
func (ctrl *MailController) SendEmailWithCC(c *gin.Context) {
	var req struct {
		To      []string `json:"to" binding:"required"`
		CC      []string `json:"cc"`
		Subject string   `json:"subject" binding:"required"`
		Message string   `json:"message" binding:"required"`
		IsHTML  bool     `json:"is_html"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Send email with CC
	err := mail.SendEmailWithCC(req.To, req.CC, req.Subject, req.Message, req.IsHTML)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to send email",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Email with CC sent successfully",
	})
}

// SendBulkEmail sends emails to multiple recipients
func (ctrl *MailController) SendBulkEmail(c *gin.Context) {
	var req struct {
		Recipients []string `json:"recipients" binding:"required"`
		Subject    string   `json:"subject" binding:"required"`
		Message    string   `json:"message" binding:"required"`
		IsHTML     bool     `json:"is_html"`
		Individual bool     `json:"individual"` // If true, send individual emails
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var err error
	if req.Individual {
		// Send individual emails to each recipient
		err = mail.SendBulkEmailIndividual(req.Recipients, req.Subject, req.Message, req.IsHTML)
	} else {
		// Send one email to all recipients (they'll see each other)
		err = mail.SendBulkEmail(req.Recipients, req.Subject, req.Message, req.IsHTML)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to send bulk email",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Bulk email sent successfully",
	})
}

// Example of setting up routes
func SetupMailRoutes(router *gin.Engine) {
	userCtrl := &UserController{}
	deviceCtrl := &DeviceController{}
	notificationCtrl := &NotificationController{}
	mailCtrl := &MailController{}

	// User routes
	router.POST("/users", userCtrl.CreateUser)
	router.POST("/users/password-reset", userCtrl.RequestPasswordReset)

	// Device routes
	router.POST("/devices/alert", deviceCtrl.SendDeviceAlert)

	// Notification routes
	router.POST("/notifications/send", notificationCtrl.SendNotification)

	// Mail routes
	router.POST("/mail/send", mailCtrl.SendCustomEmail)
	router.POST("/mail/send-with-cc", mailCtrl.SendEmailWithCC)
	router.POST("/mail/bulk", mailCtrl.SendBulkEmail)
}

func main() {
	router := gin.Default()
	SetupMailRoutes(router)
	router.Run(":8080")
}
