package main

import (
	"fmt"
	"log"
	"yotracker/internal/mail"
	"yotracker/internal/service"
)

func main() {
	// Example 1: Using the new template system with helper functions
	fmt.Println("Example 1: Welcome email with professional template")
	err := mail.SendWelcomeEmail("<EMAIL>", "John Doe")
	if err != nil {
		log.Printf("Failed to send welcome email: %v", err)
	} else {
		fmt.Println("Professional welcome email sent successfully!")
	}

	// Example 2: Password reset with template
	fmt.Println("\nExample 2: Password reset email with template")
	err = mail.SendPasswordResetEmail("<EMAIL>", "John Doe", "https://yourdomain.com/reset?token=abc123")
	if err != nil {
		log.Printf("Failed to send password reset email: %v", err)
	} else {
		fmt.Println("Professional password reset email sent successfully!")
	}

	// Example 3: Device alert with template
	fmt.Println("\nExample 3: Device alert with template")
	err = mail.SendDeviceAlert("<EMAIL>", "DEV001", "Low Battery", "Battery level is below 10%")
	if err != nil {
		log.Printf("Failed to send device alert: %v", err)
	} else {
		fmt.Println("Professional device alert sent successfully!")
	}

	// Example 4: Custom notification with template
	fmt.Println("\nExample 4: Custom notification with template")
	err = mail.SendNotificationEmail("<EMAIL>", "John Doe", "Your monthly report is ready for review.")
	if err != nil {
		log.Printf("Failed to send notification: %v", err)
	} else {
		fmt.Println("Professional notification sent successfully!")
	}

	// Example 5: Using the service directly for custom templates
	fmt.Println("\nExample 5: Custom template using service directly")
	mailService, err := service.NewMailService()
	if err != nil {
		log.Printf("Failed to create mail service: %v", err)
		return
	}

	err = mailService.SendTemplatedEmail("<EMAIL>", "Project Update", func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting("Hello John,").
			AddContent(
				"Your project 'YoTracker Implementation' has been updated.",
				"The following changes have been made:",
				"• Added professional email templates",
				"• Implemented Laravel-style design",
				"• Added action buttons and subcopy sections",
				"Please review the changes and let us know if you have any questions.",
			).
			SetAction("View Project", template.AppURL+"/projects/123")
	})

	if err != nil {
		log.Printf("Failed to send custom template email: %v", err)
	} else {
		fmt.Println("Custom template email sent successfully!")
	}

	// Example 6: Invoice email with attachment
	fmt.Println("\nExample 6: Invoice email with attachment")
	// Simulate PDF content
	pdfContent := []byte("This would be actual PDF content in a real application")
	err = mail.SendInvoiceEmail("<EMAIL>", "John Doe", "INV-2025-001", pdfContent)
	if err != nil {
		log.Printf("Failed to send invoice email: %v", err)
	} else {
		fmt.Println("Professional invoice email with attachment sent successfully!")
	}

	// Example 7: Creating a custom template manually
	fmt.Println("\nExample 7: Manual template creation")
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting("Welcome to our Beta Program!").
		AddContent(
			"You've been selected to participate in our exclusive beta program.",
			"As a beta tester, you'll get early access to new features and the ability to shape the future of YoTracker.",
			"Your feedback is invaluable to us, and we're excited to have you on board.",
		).
		SetAction("Join Beta Program", template.AppURL+"/beta/join").
		SetSignature("The YoTracker Beta Team")

	htmlBody := template.GenerateHTML()
	textBody := template.GenerateText()

	// You can now use these in any way you want
	fmt.Printf("Generated HTML length: %d characters\n", len(htmlBody))
	fmt.Printf("Generated Text length: %d characters\n", len(textBody))

	fmt.Println("\nAll template examples completed!")
	fmt.Println("\nThe emails now feature:")
	fmt.Println("✅ Professional Laravel-style design")
	fmt.Println("✅ Responsive layout")
	fmt.Println("✅ Action buttons with proper styling")
	fmt.Println("✅ Header with app branding")
	fmt.Println("✅ Subcopy section for button alternatives")
	fmt.Println("✅ Professional footer with copyright")
	fmt.Println("✅ Both HTML and plain text versions")
}

// Example of how to use templates in your controllers
func SendProjectNotification(userEmail, userName, projectName, projectURL string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	subject := fmt.Sprintf("Project Update: %s", projectName)
	
	return mailService.SendTemplatedEmail(userEmail, subject, func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Hello %s,", userName)).
			AddContent(
				fmt.Sprintf("Your project '%s' has been updated.", projectName),
				"Please review the latest changes and provide your feedback.",
			).
			SetAction("View Project", projectURL)
	})
}

// Example of a comment notification
func SendCommentNotification(userEmail, userName, commenterName, comment, projectURL string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendTemplatedEmail(userEmail, "New Comment Added", func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Dear %s", userName)).
			AddContent(
				fmt.Sprintf("A new comment has been added to your project by %s.", commenterName),
				fmt.Sprintf("Comment: %s", comment),
			).
			SetAction("View Project", projectURL)
	})
}

// Example of a system maintenance notification
func SendMaintenanceNotification(userEmail, userName, maintenanceDate, duration string) error {
	mailService, err := service.NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	return mailService.SendTemplatedEmail(userEmail, "Scheduled Maintenance Notification", func(template *service.EmailTemplate) *service.EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Dear %s,", userName)).
			AddContent(
				"We will be performing scheduled maintenance on our systems.",
				fmt.Sprintf("Maintenance Date: %s", maintenanceDate),
				fmt.Sprintf("Expected Duration: %s", duration),
				"During this time, some services may be temporarily unavailable.",
				"We apologize for any inconvenience and appreciate your patience.",
			).
			SetAction("View Status Page", template.AppURL+"/status")
	})
}
